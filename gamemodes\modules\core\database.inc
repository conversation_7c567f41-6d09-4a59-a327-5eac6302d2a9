#include <YSI_Coding\y_hooks>
new MySQL:g_iHandle;

SQL_Connect() {
	g_iHandle = mysql_connect_file();

	if (mysql_errno(g_iHandle) != 0) {
	    printf("[SQL] Connection to database failed! Please check the connection settings...\a");
	}
	else {
		printf("[SQL] Connection to database passed!");
	}
	return 1;
}

stock SQL_ReturnEscaped(const string[])
{
	new
	    entry[256];

	mysql_escape_string(string, entry);
	return entry;
}



hook OnGameModeInit()
{
	SQL_Connect();
	return 1;
}