# Auto Database Column Creator System

## Overview
Sistem ini secara otomatis membuat kolom database yang hilang saat server startup, mencegah error MySQL ketika fitur baru ditambahkan tapi database belum diupdate secara manual.

## Cara Kerja
1. Server start dan connect ke MySQL
2. System check array `RequiredColumns` untuk semua kolom yang dibutuhkan
3. Mencoba create setiap kolom dengan `ALTER TABLE`
4. <PERSON>ka kolom sudah ada (error 1060), akan di-skip
5. Jika kolom berhasil dibuat, akan di-log
6. <PERSON>ka ada error lain, akan dilaporkan

## Cara Menambah Kolom Baru

### 1. Edit File auto_database.inc
Buka file `gamemodes/modules/core/auto_database.inc` dan tambahkan entry baru ke array `RequiredColumns`:

```pawn
// Tambahkan di array RequiredColumns
{"table_name", "column_name", "column_type", "default_value"}
```

### 2. <PERSON><PERSON><PERSON>

```pawn
// Characters table - kolom untuk fitur baru
{"characters", "last_mine_time", "INT(11)", "0"},
{"characters", "new_feature_time", "INT(11)", "0"},
{"characters", "player_status", "VARCHAR(32)", "'active'"},

// Cars table - kolom untuk modifikasi kendaraan
{"cars", "carSpoiler", "INT(11)", "0"},
{"cars", "carNitro", "INT(11)", "0"},

// Houses table - kolom untuk fitur rumah baru
{"houses", "house_security_level", "INT(11)", "1"},
{"houses", "house_last_visited", "DATETIME", "NOW()"}
```

### 3. Restart Server
Setelah menambahkan kolom, restart server. Kolom akan dibuat otomatis.

## Tipe Data yang Didukung

| Tipe | Deskripsi | Contoh |
|------|-----------|---------|
| `INT(11)` | Angka integer | `"0"`, `"1"`, `"-1"` |
| `VARCHAR(32)` | Text string | `"'default'"`, `"'none'"` |
| `FLOAT` | Angka desimal | `"0.0"`, `"100.0"` |
| `TEXT` | Text panjang | `"''"` |
| `DATETIME` | Tanggal dan waktu | `"NOW()"`, `"'1970-01-01 00:00:00'"` |

## Log Output

Saat server start, Anda akan melihat output seperti ini:

```
[AUTO-DATABASE] Starting auto-create process for 25 columns...
[AUTO-DATABASE] ✓ Created: characters.last_mine_time
[AUTO-DATABASE] ✓ Exists: cars.carSpoiler
[AUTO-DATABASE] ✓ Created: cars.carNitro
[AUTO-DATABASE] ✗ Failed: invalid_table.test_column (Error: 1146 - Table doesn't exist)
========================================
[AUTO-DATABASE] Process completed!
Total columns checked: 25
Created: 15 | Already existed: 9 | Failed: 1
All database columns are now ready!
========================================
```

## Troubleshooting

### Error: Table doesn't exist
```
[AUTO-DATABASE] ✗ Failed: table_name.column_name (Error: 1146 - Table doesn't exist)
```
**Solusi:** Pastikan nama tabel benar dan tabel sudah ada di database.

### Error: Access denied
```
[AUTO-DATABASE] ✗ Failed: table_name.column_name (Error: 1142 - ALTER command denied)
```
**Solusi:** Pastikan user MySQL memiliki permission ALTER pada database.

### Error: Duplicate column name
```
[AUTO-DATABASE] ✓ Exists: table_name.column_name
```
**Status:** Normal - kolom sudah ada, tidak perlu action.

## Best Practices

### 1. Naming Convention
- Gunakan camelCase untuk nama kolom: `lastMineTime`, `carSpoiler`
- Gunakan prefix yang konsisten: `car*`, `house*`, `player*`

### 2. Default Values
- Selalu berikan default value yang masuk akal
- Untuk INT: gunakan `"0"` atau `"-1"`
- Untuk VARCHAR: gunakan `"'default'"` atau `"'none'"`
- Untuk DATETIME: gunakan `"NOW()"` atau `"'1970-01-01 00:00:00'"`

### 3. Testing
- Test di development server dulu sebelum production
- Backup database sebelum menambah kolom baru
- Monitor log untuk memastikan semua kolom berhasil dibuat

## File yang Terkait

- `gamemodes/modules/core/auto_database.inc` - File utama sistem
- `gamemodes/modules/core/database.inc` - Koneksi MySQL
- `gamemodes/CGRP.pwn` - Include auto_database.inc

## Maintenance

### Menambah Kolom Baru
1. Edit `RequiredColumns` array
2. Restart server
3. Check log untuk konfirmasi

### Menghapus Kolom Lama
1. Hapus entry dari `RequiredColumns` array
2. Kolom di database TIDAK akan dihapus otomatis (untuk safety)
3. Hapus manual jika diperlukan: `ALTER TABLE table_name DROP COLUMN column_name`

## Security Notes

- System hanya menambah kolom, tidak menghapus
- Tidak mengubah data yang sudah ada
- Safe untuk dijalankan berulang kali
- Tidak mempengaruhi performance server (hanya jalan saat startup)
