# Panduan Menangani Error "Duplicate Column Name"

## Penyebab Error
Error `** [MySQL]: Duplicate column name 'column_name'` terjadi ketika:
1. Kolom sudah ada di database tapi kode mencoba membuatnya lagi
2. Ada multiple ALTER TABLE statement yang mencoba membuat kolom yang sama
3. Server restart berkali-kali dan sistem auto-database berjalan berulang

## Solusi yang Telah Diimplementasi

### 1. Sistem Auto-Database (Otomatis)
Sistem auto-database sudah menangani duplicate column dengan:
- **Pre-check**: Cek kolom sebelum membuat
- **Error handling**: Handle error 1060 (Duplicate column name)
- **Smart skip**: Skip kolom yang sudah ada

### 2. Fungsi SafeAddColumn (Manual)
Untuk developer yang ingin menambah kolom manual:

```pawn
// Gunakan ini INSTEAD OF direct ALTER TABLE
AutoDatabase_SafeAddColumn("table_name", "column_name", "INT(11)", "0");

// JANGAN gunakan ini:
// mysql_query(g_iHandle, "ALTER TABLE `table` ADD COLUMN `column` INT(11) DEFAULT 0");
```

### 3. Fungsi CheckColumn (Utility)
Untuk mengecek apakah kolom ada:

```pawn
// Check if column exists before doing something
AutoDatabase_CheckColumn("characters", "last_mine_time", "OnColumnCheckResult");

forward OnColumnCheckResult(table[], column[]);
public OnColumnCheckResult(table[], column[]) {
    if (cache_num_rows() > 0) {
        printf("Column %s.%s exists!", table, column);
    } else {
        printf("Column %s.%s does not exist!", table, column);
    }
}
```

## Cara Mengatasi Error yang Sudah Terjadi

### Jika Error Muncul Saat Server Start:
1. **Cek log server** - Lihat kolom mana yang duplicate
2. **Restart server** - Sistem auto-database akan handle otomatis
3. **Monitor log** - Pastikan muncul "✓ Exists: table.column"

### Jika Error Muncul di Kode Custom:
1. **Ganti ALTER TABLE** dengan `AutoDatabase_SafeAddColumn()`
2. **Compile ulang** gamemode
3. **Test** fitur yang bermasalah

## Contoh Log Normal (Tidak Error)

```
[AUTO-DATABASE] Starting auto-create process for 386 columns...
[AUTO-DATABASE] ✓ Exists: characters.last_mine_time
[AUTO-DATABASE] ✓ Created: characters.new_feature
[AUTO-DATABASE] ✓ Exists: cars.carSpoiler
[AUTO-DATABASE] ✓ Created: cars.carNewMod
========================================
[AUTO-DATABASE] Process completed!
Total columns checked: 386
Created: 50 | Already existed: 336 | Failed: 0
All database columns are now ready!
========================================
```

## Contoh Log Error (Perlu Diperbaiki)

```
** [MySQL]: Duplicate column name 'last_mine_time'
```

**Solusi:** Gunakan `AutoDatabase_SafeAddColumn()` instead of direct ALTER TABLE.

## Best Practices

### DO ✅
- Gunakan sistem auto-database untuk kolom baru
- Gunakan `AutoDatabase_SafeAddColumn()` untuk manual add
- Cek log server untuk memastikan tidak ada error
- Restart server jika ada perubahan database

### DON'T ❌
- Jangan gunakan direct `ALTER TABLE ADD COLUMN` 
- Jangan ignore error duplicate column
- Jangan manual edit database tanpa update kode
- Jangan restart server berkali-kali tanpa cek log

## Troubleshooting

### Error: "Table doesn't exist"
```
[AUTO-DATABASE] ✗ Failed to check: table_name.column_name (Error: 1146 - Table doesn't exist)
```
**Solusi:** Pastikan tabel sudah dibuat di database.

### Error: "Access denied"
```
[AUTO-DATABASE] ✗ Failed: table_name.column_name (Error: 1142 - ALTER command denied)
```
**Solusi:** Pastikan user MySQL memiliki permission ALTER.

### Error: "Unknown column in field list"
```
** [MySQL]: Unknown column 'column_name' in 'field list'
```
**Solusi:** Tambahkan kolom ke array RequiredColumns dan restart server.

## Support

Jika masih ada masalah:
1. Cek file log server
2. Pastikan sistem auto-database aktif
3. Gunakan fungsi SafeAddColumn untuk kode custom
4. Restart server dan monitor log
