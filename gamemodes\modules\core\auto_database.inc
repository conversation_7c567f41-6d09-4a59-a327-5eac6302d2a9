/*
	=============================================================================
	AUTO DATABASE COLUMN CREATOR SYSTEM
	=============================================================================

	DESCRIPTION:
	Automatically creates missing database columns when server starts.
	This prevents MySQL errors when new features are added but database isn't updated manually.

	HOW IT WORKS:
	1. Server starts and connects to MySQL
	2. System checks RequiredColumns array for all needed columns
	3. Attempts to create each column with ALTER TABLE
	4. If column exists (error 1060), it's skipped
	5. If column is created successfully, it's logged
	6. If other error occurs, it's reported

	HOW TO ADD NEW COLUMNS:
	1. Add entry to RequiredColumns array below
	2. Format: {"table_name", "column_name", "column_type", "default_value"}
	3. Restart server - column will be created automatically

	EXAMPLE:
	{"characters", "new_feature_time", "INT(11)", "0"}
	{"cars", "new_car_feature", "VARCHAR(32)", "'default'"}

	SUPPORTED COLUMN TYPES:
	- INT(11) - Integer numbers
	- VARCHAR(32) - Text strings (specify length)
	- FLOAT - Decimal numbers
	- TEXT - Long text
	- DATETIME - Date and time

	HANDLING DUPLICATE COLUMN ERRORS:
	If you get "Duplicate column name" error outside this system:
	1. Use AutoDatabase_SafeAddColumn() instead of direct ALTER TABLE
	2. This function checks column existence before creating
	3. Safe for use in any part of the gamemode

	EXAMPLE USAGE:
	AutoDatabase_SafeAddColumn("characters", "new_column", "INT(11)", "0");

	=============================================================================
*/

#include <YSI_Coding\y_hooks>

// Structure to define required database columns
enum ColumnInfo {
	col_table[32],
	col_name[32], 
	col_type[64],
	col_default[32]
}

// Define all required database columns here
// Add new columns to this array when adding new features
new RequiredColumns[][ColumnInfo] = {
	// ===== CHARACTERS TABLE - Player Data =====
	{"characters", "last_mine_time", "INT(11)", "0"},
	{"characters", "reward_claimed", "INT(11)", "0"},

	// Body parts for injury system
	{"characters", "Torso", "INT(11)", "100"},
	{"characters", "Groin", "INT(11)", "100"},
	{"characters", "Left Arm", "INT(11)", "100"},
	{"characters", "Right Arm", "INT(11)", "100"},
	{"characters", "Left Leg", "INT(11)", "100"},
	{"characters", "Right Leg", "INT(11)", "100"},
	{"characters", "Head", "INT(11)", "100"},
	{"characters", "MedicineTime", "INT(11)", "0"},

	// Warning system
	{"characters", "Warn1", "VARCHAR(32)", "'None'"},
	{"characters", "Warn2", "VARCHAR(32)", "'None'"},

	// Weapon and ammo slots (13 slots)
	{"characters", "Gun1", "INT(11)", "0"},
	{"characters", "Gun2", "INT(11)", "0"},
	{"characters", "Gun3", "INT(11)", "0"},
	{"characters", "Gun4", "INT(11)", "0"},
	{"characters", "Gun5", "INT(11)", "0"},
	{"characters", "Gun6", "INT(11)", "0"},
	{"characters", "Gun7", "INT(11)", "0"},
	{"characters", "Gun8", "INT(11)", "0"},
	{"characters", "Gun9", "INT(11)", "0"},
	{"characters", "Gun10", "INT(11)", "0"},
	{"characters", "Gun11", "INT(11)", "0"},
	{"characters", "Gun12", "INT(11)", "0"},
	{"characters", "Gun13", "INT(11)", "0"},

	{"characters", "Ammo1", "INT(11)", "0"},
	{"characters", "Ammo2", "INT(11)", "0"},
	{"characters", "Ammo3", "INT(11)", "0"},
	{"characters", "Ammo4", "INT(11)", "0"},
	{"characters", "Ammo5", "INT(11)", "0"},
	{"characters", "Ammo6", "INT(11)", "0"},
	{"characters", "Ammo7", "INT(11)", "0"},
	{"characters", "Ammo8", "INT(11)", "0"},
	{"characters", "Ammo9", "INT(11)", "0"},
	{"characters", "Ammo10", "INT(11)", "0"},
	{"characters", "Ammo11", "INT(11)", "0"},
	{"characters", "Ammo12", "INT(11)", "0"},
	{"characters", "Ammo13", "INT(11)", "0"},

	// Fish inventory (10 fish types)
	{"characters", "Fish1", "INT(11)", "0"},
	{"characters", "Fish2", "INT(11)", "0"},
	{"characters", "Fish3", "INT(11)", "0"},
	{"characters", "Fish4", "INT(11)", "0"},
	{"characters", "Fish5", "INT(11)", "0"},
	{"characters", "Fish6", "INT(11)", "0"},
	{"characters", "Fish7", "INT(11)", "0"},
	{"characters", "Fish8", "INT(11)", "0"},
	{"characters", "Fish9", "INT(11)", "0"},
	{"characters", "Fish10", "INT(11)", "0"},

	// Fish weights
	{"characters", "Weight1", "INT(11)", "0"},
	{"characters", "Weight2", "INT(11)", "0"},
	{"characters", "Weight3", "INT(11)", "0"},
	{"characters", "Weight4", "INT(11)", "0"},
	{"characters", "Weight5", "INT(11)", "0"},
	{"characters", "Weight6", "INT(11)", "0"},
	{"characters", "Weight7", "INT(11)", "0"},
	{"characters", "Weight8", "INT(11)", "0"},
	{"characters", "Weight9", "INT(11)", "0"},
	{"characters", "Weight10", "INT(11)", "0"},

	// ===== CARS TABLE - Vehicle Modifications =====
	// Vehicle modification status (0/1)
	{"cars", "carNitro", "INT(11)", "0"},
	{"cars", "carHydraulic", "INT(11)", "0"},
	{"cars", "carSpoiler", "INT(11)", "0"},
	{"cars", "carFrontBumper", "INT(11)", "0"},
	{"cars", "carRearBumper", "INT(11)", "0"},
	{"cars", "carLeftSideskirt", "INT(11)", "0"},
	{"cars", "carRightSideskirt", "INT(11)", "0"},
	{"cars", "carRoof", "INT(11)", "0"},
	{"cars", "carExhaust", "INT(11)", "0"},

	// Component IDs (actual SA-MP component numbers)
	{"cars", "carSpoilerID", "INT(11)", "0"},
	{"cars", "carFrontBumperID", "INT(11)", "0"},
	{"cars", "carRearBumperID", "INT(11)", "0"},
	{"cars", "carLeftSideskirtID", "INT(11)", "0"},
	{"cars", "carRightSideskirtID", "INT(11)", "0"},
	{"cars", "carRoofID", "INT(11)", "0"},
	{"cars", "carExhaustID", "INT(11)", "0"},

	// Car weapon storage (3 weapon slots)
	{"cars", "carWeapon1", "INT(11)", "0"},
	{"cars", "carWeapon2", "INT(11)", "0"},
	{"cars", "carWeapon3", "INT(11)", "0"},
	{"cars", "carAmmo1", "INT(11)", "0"},
	{"cars", "carAmmo2", "INT(11)", "0"},
	{"cars", "carAmmo3", "INT(11)", "0"},

	// Car modifications (14 mod slots)
	{"cars", "carMod1", "INT(11)", "0"},
	{"cars", "carMod2", "INT(11)", "0"},
	{"cars", "carMod3", "INT(11)", "0"},
	{"cars", "carMod4", "INT(11)", "0"},
	{"cars", "carMod5", "INT(11)", "0"},
	{"cars", "carMod6", "INT(11)", "0"},
	{"cars", "carMod7", "INT(11)", "0"},
	{"cars", "carMod8", "INT(11)", "0"},
	{"cars", "carMod9", "INT(11)", "0"},
	{"cars", "carMod10", "INT(11)", "0"},
	{"cars", "carMod11", "INT(11)", "0"},
	{"cars", "carMod12", "INT(11)", "0"},
	{"cars", "carMod13", "INT(11)", "0"},
	{"cars", "carMod14", "INT(11)", "0"},

	// ===== MATERIALLOTS TABLE - Material System (Real table name from database) =====
	{"materiallots", "materialID", "INT(11)", "0"},
	{"materiallots", "materialPrice", "INT(11)", "0"},
	{"materiallots", "materialProducts", "INT(11)", "0"},
	{"materiallots", "materialInterior", "INT(11)", "0"},
	{"materiallots", "materialVirtual", "INT(11)", "0"},
	{"materiallots", "materialPosX", "FLOAT", "0.0"},
	{"materiallots", "materialPosY", "FLOAT", "0.0"},
	{"materiallots", "materialPosZ", "FLOAT", "0.0"},

	// ===== REAL TABLES FROM DATABASE =====

	// Gates table (real name: gates)
	{"gates", "gateID", "INT(11)", "0"},
	{"gates", "gateX", "FLOAT", "0.0"},
	{"gates", "gateY", "FLOAT", "0.0"},
	{"gates", "gateZ", "FLOAT", "0.0"},
	{"gates", "gateModel", "INT(11)", "0"},
	{"gates", "gateInterior", "INT(11)", "0"},
	{"gates", "gateWorld", "INT(11)", "0"},

	// Speed cameras table (real name: speedcameras)
	{"speedcameras", "speedID", "INT(11)", "0"},
	{"speedcameras", "speedRange", "FLOAT", "0.0"},
	{"speedcameras", "speedLimit", "FLOAT", "0.0"},
	{"speedcameras", "speedX", "FLOAT", "0.0"},
	{"speedcameras", "speedY", "FLOAT", "0.0"},
	{"speedcameras", "speedZ", "FLOAT", "0.0"},
	{"speedcameras", "speedAngle", "FLOAT", "0.0"},

	// Storage tables (real names from database)
	{"fish_warehouse", "stock", "INT(11)", "0"},
	{"component_storage", "stock", "INT(11)", "0"},
	{"plant_storage", "stock", "INT(11)", "0"},

	// Animal lots table (real name: animallots)
	{"animallots", "animalID", "INT(11)", "0"},
	{"animallots", "animalType", "INT(11)", "0"},
	{"animallots", "animalPosX", "FLOAT", "0.0"},
	{"animallots", "animalPosY", "FLOAT", "0.0"},
	{"animallots", "animalPosZ", "FLOAT", "0.0"},
	{"animallots", "animalPosRX", "FLOAT", "0.0"},
	{"animallots", "animalPosRY", "FLOAT", "0.0"},
	{"animallots", "animalPosRZ", "FLOAT", "0.0"},
	{"animallots", "animalTime", "INT(11)", "0"},

	// Vendors table (real name: vendors)
	{"vendors", "vendorID", "INT(11)", "0"},
	{"vendors", "vendorType", "INT(11)", "0"},
	{"vendors", "vendorX", "FLOAT", "0.0"},
	{"vendors", "vendorY", "FLOAT", "0.0"},
	{"vendors", "vendorZ", "FLOAT", "0.0"},
	{"vendors", "vendorA", "FLOAT", "0.0"},
	{"vendors", "vendorInterior", "INT(11)", "0"},
	{"vendors", "vendorWorld", "INT(11)", "0"},

	// Optional feature tables - uncomment if these tables exist:
	// {"redeem_codes", "id", "INT(11)", "0"},
	// {"redeem_codes", "code", "VARCHAR(64)", "'default'"},
	// {"redeem_codes", "reward_type", "VARCHAR(32)", "'money'"},
	// {"redeem_codes", "reward_item", "VARCHAR(64)", "'default'"},
	// {"redeem_codes", "amount", "INT(11)", "0"},
	// {"redeem_codes", "created_at", "DATETIME", "NOW()"},

	// {"plants", "plantID", "INT(11)", "0"},
	// {"plants", "plantType", "INT(11)", "0"},
	// {"plants", "plantDrugs", "INT(11)", "0"},
	// {"plants", "plantX", "FLOAT", "0.0"},
	// {"plants", "plantY", "FLOAT", "0.0"},
	// {"plants", "plantZ", "FLOAT", "0.0"},
	// {"plants", "plantA", "FLOAT", "0.0"},
	// {"plants", "plantInterior", "INT(11)", "0"},
	// {"plants", "plantWorld", "INT(11)", "0"},

	// {"detectors", "detectorID", "INT(11)", "0"},
	// {"detectors", "detectorX", "FLOAT", "0.0"},
	// {"detectors", "detectorY", "FLOAT", "0.0"},
	// {"detectors", "detectorZ", "FLOAT", "0.0"},
	// {"detectors", "detectorAngle", "FLOAT", "0.0"},
	// {"detectors", "detectorInterior", "INT(11)", "0"},
	// {"detectors", "detectorWorld", "INT(11)", "0"},

	// ===== PLAYER ACCESSORIES - Using existing tables =====
	// Note: Player accessories might be stored in inventory or caracc table
	// {"player_accessories", "accID", "INT(11)", "0"}, // Table doesn't exist

	// ===== SALARY TABLE - Player Salary System (real name: salary) =====
	{"salary", "salaryID", "INT(11)", "0"},
	{"salary", "salaryAmmount", "INT(11)", "0"},
	{"salary", "salaryTime", "INT(11)", "0"},
	{"salary", "salaryName", "VARCHAR(32)", "'Salary'"},

	// ===== HOUSES TABLE - House System =====
	{"houses", "houseID", "INT(11)", "0"},
	{"houses", "houseOwner", "VARCHAR(24)", "'None'"},
	{"houses", "housePrice", "INT(11)", "0"},
	{"houses", "houseLocked", "INT(11)", "0"},
	{"houses", "houseInterior", "INT(11)", "0"},
	{"houses", "houseWorld", "INT(11)", "0"},
	{"houses", "housePosX", "FLOAT", "0.0"},
	{"houses", "housePosY", "FLOAT", "0.0"},
	{"houses", "housePosZ", "FLOAT", "0.0"},

	// ===== BUSINESS TABLE - Business System =====
	{"business", "businessID", "INT(11)", "0"},
	{"business", "businessOwner", "VARCHAR(24)", "'None'"},
	{"business", "businessName", "VARCHAR(32)", "'Business'"},
	{"business", "businessType", "INT(11)", "0"},
	{"business", "businessPrice", "INT(11)", "0"},
	{"business", "businessLocked", "INT(11)", "0"},
	{"business", "businessInterior", "INT(11)", "0"},
	{"business", "businessWorld", "INT(11)", "0"},
	{"business", "businessPosX", "FLOAT", "0.0"},
	{"business", "businessPosY", "FLOAT", "0.0"},
	{"business", "businessPosZ", "FLOAT", "0.0"},

	// ===== FACTIONS TABLE - Faction System =====
	{"factions", "factionID", "INT(11)", "0"},
	{"factions", "factionName", "VARCHAR(32)", "'Faction'"},
	{"factions", "factionType", "INT(11)", "0"},
	{"factions", "factionColor", "INT(11)", "0"},
	{"factions", "factionRanks", "INT(11)", "5"},
	{"factions", "factionMembers", "INT(11)", "0"},
	{"factions", "factionBudget", "INT(11)", "0"},

	// ===== ATMS TABLE - ATM System =====
	{"atms", "atmID", "INT(11)", "0"},
	{"atms", "atmPosX", "FLOAT", "0.0"},
	{"atms", "atmPosY", "FLOAT", "0.0"},
	{"atms", "atmPosZ", "FLOAT", "0.0"},
	{"atms", "atmAngle", "FLOAT", "0.0"},
	{"atms", "atmInterior", "INT(11)", "0"},
	{"atms", "atmWorld", "INT(11)", "0"},

	// ===== BILLBOARDS TABLE - Billboard System =====
	{"billboards", "billboardID", "INT(11)", "0"},
	{"billboards", "billboardOwner", "VARCHAR(24)", "'None'"},
	{"billboards", "billboardText", "VARCHAR(128)", "'Advertisement'"},
	{"billboards", "billboardPrice", "INT(11)", "0"},
	{"billboards", "billboardPosX", "FLOAT", "0.0"},
	{"billboards", "billboardPosY", "FLOAT", "0.0"},
	{"billboards", "billboardPosZ", "FLOAT", "0.0"},

	// ===== ENTRANCES TABLE - Entrance System =====
	{"entrances", "entranceID", "INT(11)", "0"},
	{"entrances", "entranceType", "INT(11)", "0"},
	{"entrances", "entrancePosX", "FLOAT", "0.0"},
	{"entrances", "entrancePosY", "FLOAT", "0.0"},
	{"entrances", "entrancePosZ", "FLOAT", "0.0"},
	{"entrances", "entranceIntX", "FLOAT", "0.0"},
	{"entrances", "entranceIntY", "FLOAT", "0.0"},
	{"entrances", "entranceIntZ", "FLOAT", "0.0"},
	{"entrances", "entranceInterior", "INT(11)", "0"},
	{"entrances", "entranceWorld", "INT(11)", "0"},

	// ===== INVENTORY TABLE - Player Inventory System (real name: inventory) =====
	{"inventory", "invID", "INT(11)", "0"},
	{"inventory", "invItem", "VARCHAR(32)", "'None'"},
	{"inventory", "invModel", "INT(11)", "0"},
	{"inventory", "invQuantity", "INT(11)", "0"},
	{"inventory", "invSlot", "INT(11)", "0"},

	// ===== CONTACTS TABLE - Player Contact System (real name: contacts) =====
	{"contacts", "contactID", "INT(11)", "0"},
	{"contacts", "contactName", "VARCHAR(24)", "'Contact'"},
	{"contacts", "contactNumber", "INT(11)", "0"},

	// ===== TICKETS TABLE - Player Ticket System (real name: tickets) =====
	{"tickets", "ticketID", "INT(11)", "0"},
	{"tickets", "ticketReason", "VARCHAR(64)", "'Traffic Violation'"},
	{"tickets", "ticketDate", "VARCHAR(36)", "'1970-01-01'"},
	{"tickets", "ticketFee", "INT(11)", "0"},

	// ===== GPS TABLE - GPS Location System (real name: gps) =====
	{"gps", "locationID", "INT(11)", "0"},
	{"gps", "locationName", "VARCHAR(32)", "'Location'"},
	{"gps", "locationX", "FLOAT", "0.0"},
	{"gps", "locationY", "FLOAT", "0.0"},
	{"gps", "locationZ", "FLOAT", "0.0"},

	// ===== WORKSHOP TABLE - Workshop System (real name: workshop) =====
	{"workshop", "workshopID", "INT(11)", "0"},
	{"workshop", "workshopType", "INT(11)", "0"},
	{"workshop", "workshopPosX", "FLOAT", "0.0"},
	{"workshop", "workshopPosY", "FLOAT", "0.0"},
	{"workshop", "workshopPosZ", "FLOAT", "0.0"},
	{"workshop", "workshopInterior", "INT(11)", "0"},
	{"workshop", "workshopWorld", "INT(11)", "0"},

	// ===== TREES TABLE - Tree System =====
	{"trees", "treeID", "INT(11)", "0"},
	{"trees", "treeType", "INT(11)", "0"},
	{"trees", "treePosX", "FLOAT", "0.0"},
	{"trees", "treePosY", "FLOAT", "0.0"},
	{"trees", "treePosZ", "FLOAT", "0.0"},
	{"trees", "treeHealth", "INT(11)", "100"},

	// ===== GRAFFITI TABLE - Graffiti System (real name: graffiti) =====
	{"graffiti", "graffitiID", "INT(11)", "0"},
	{"graffiti", "graffitiText", "VARCHAR(64)", "'Graffiti'"},
	{"graffiti", "graffitiPosX", "FLOAT", "0.0"},
	{"graffiti", "graffitiPosY", "FLOAT", "0.0"},
	{"graffiti", "graffitiPosZ", "FLOAT", "0.0"},
	{"graffiti", "graffitiColor", "INT(11)", "0"},

	// ===== IMPOUNDLOTS TABLE - Vehicle Impound System (real name: impoundlots) =====
	{"impoundlots", "impoundID", "INT(11)", "0"},
	{"impoundlots", "impoundVehicle", "INT(11)", "0"},
	{"impoundlots", "impoundPrice", "INT(11)", "0"},
	{"impoundlots", "impoundTime", "INT(11)", "0"},
	{"impoundlots", "impoundReason", "VARCHAR(128)", "'Traffic Violation'"},

	// Note: 'reports' table doesn't exist in database - commenting out
	// {"reports", "reportID", "INT(11)", "0"},
	// {"reports", "reportType", "VARCHAR(32)", "'General'"},
	// {"reports", "reportText", "TEXT", "''"},
	// {"reports", "reportStatus", "INT(11)", "0"},
	// {"reports", "reportTime", "INT(11)", "0"},

	// ===== ACCOUNTS TABLE - Additional Account Columns =====
	{"accounts", "email", "VARCHAR(64)", "'<EMAIL>'"},
	{"accounts", "isVerified", "INT(11)", "0"},
	{"accounts", "discord_code", "INT(11)", "0"},
	{"accounts", "LoginDate", "VARCHAR(32)", "'1970-01-01'"},
	{"accounts", "createdAt", "VARCHAR(32)", "'1970-01-01'"},
	{"accounts", "IP", "VARCHAR(16)", "'0.0.0.0'"},
	{"accounts", "AdminHide", "INT(11)", "0"},
	{"accounts", "VIP", "INT(11)", "0"},
	{"accounts", "VIPTime", "INT(11)", "0"},
	{"accounts", "VIPCoin", "INT(11)", "0"},

	// ===== TREATMENTLOTS TABLE - Treatment System =====
	{"treatmentlots", "treatmentID", "INT(11)", "0"},
	{"treatmentlots", "treatmentPrice", "INT(11)", "0"},
	{"treatmentlots", "treatmentPosX", "FLOAT", "0.0"},
	{"treatmentlots", "treatmentPosY", "FLOAT", "0.0"},
	{"treatmentlots", "treatmentPosZ", "FLOAT", "0.0"},

	// ===== CRATES TABLE - Crate System =====
	{"crates", "crateID", "INT(11)", "0"},
	{"crates", "crateType", "INT(11)", "0"},
	{"crates", "crateX", "FLOAT", "0.0"},
	{"crates", "crateY", "FLOAT", "0.0"},
	{"crates", "crateZ", "FLOAT", "0.0"},
	{"crates", "crateA", "FLOAT", "0.0"},
	{"crates", "crateInterior", "INT(11)", "0"},
	{"crates", "crateWorld", "INT(11)", "0"},

	// ===== MOTD TABLE - Message of the Day =====
	{"motd", "id", "INT(11)", "1"},
	{"motd", "text", "TEXT", "''"}
};

// Variables for tracking column creation process
new g_ColumnIndex = 0;
new g_TotalColumns = 0;
new g_CreatedColumns = 0;
new g_ExistingColumns = 0;
new g_FailedColumns = 0;

// Initialize auto database system
forward AutoDatabase_Init();
public AutoDatabase_Init() {
	g_TotalColumns = sizeof(RequiredColumns);
	g_ColumnIndex = 0;
	g_CreatedColumns = 0;
	g_ExistingColumns = 0;
	g_FailedColumns = 0;
	
	printf("[AUTO-DATABASE] Starting auto-create process for %d columns...", g_TotalColumns);
	
	// Start the column creation process
	AutoDatabase_CreateNextColumn();
	
	return 1;
}

// Create the next column in the queue
forward AutoDatabase_CreateNextColumn();
public AutoDatabase_CreateNextColumn() {
	if (g_ColumnIndex >= g_TotalColumns) {
		// All columns processed, show summary
		AutoDatabase_ShowSummary();
		return 1;
	}

	// First check if column exists before trying to create it
	new query[512];
	format(query, sizeof(query),
		"SELECT COLUMN_NAME FROM INFORMATION_SCHEMA.COLUMNS WHERE TABLE_SCHEMA = DATABASE() AND TABLE_NAME = '%s' AND COLUMN_NAME = '%s'",
		RequiredColumns[g_ColumnIndex][col_table],
		RequiredColumns[g_ColumnIndex][col_name]
	);

	mysql_tquery(g_iHandle, query, "OnCheckColumnExists", "d", g_ColumnIndex);

	return 1;
}

// Check if column exists before creating
forward OnCheckColumnExists(column_index);
public OnCheckColumnExists(column_index) {
	new error_code = mysql_errno(g_iHandle);

	if (error_code != 0) {
		// Error occurred (probably table doesn't exist)
		g_FailedColumns++;
		new error_msg[256];
		mysql_error(error_msg, sizeof(error_msg), g_iHandle);

		if (error_code == 1146) {
			// Table doesn't exist
			printf("[AUTO-DATABASE] [SKIP] Table not found: %s (skipping %s.%s)",
				RequiredColumns[column_index][col_table],
				RequiredColumns[column_index][col_table],
				RequiredColumns[column_index][col_name]
			);
		} else {
			printf("[AUTO-DATABASE] [ERR] Failed to check: %s.%s (Error: %d - %s)",
				RequiredColumns[column_index][col_table],
				RequiredColumns[column_index][col_name],
				error_code,
				error_msg
			);
		}

		// Move to next column
		g_ColumnIndex++;
		SetTimer("AutoDatabase_CreateNextColumn", 50, false);
		return 1;
	}

	if (cache_num_rows() > 0) {
		// Column already exists
		g_ExistingColumns++;
		printf("[AUTO-DATABASE] [OK] Exists: %s.%s",
			RequiredColumns[column_index][col_table],
			RequiredColumns[column_index][col_name]
		);

		// Move to next column
		g_ColumnIndex++;
		SetTimer("AutoDatabase_CreateNextColumn", 50, false);
	} else {
		// Column doesn't exist, create it
		new query[256];
		format(query, sizeof(query),
			"ALTER TABLE `%s` ADD COLUMN `%s` %s DEFAULT %s",
			RequiredColumns[column_index][col_table],
			RequiredColumns[column_index][col_name],
			RequiredColumns[column_index][col_type],
			RequiredColumns[column_index][col_default]
		);

		mysql_tquery(g_iHandle, query, "OnAutoColumnCreated", "d", column_index);
	}

	return 1;
}

// Callback when a column creation attempt is completed
forward OnAutoColumnCreated(column_index);
public OnAutoColumnCreated(column_index) {
	new error_code = mysql_errno(g_iHandle);
	
	if (error_code == 0) {
		// Column created successfully
		g_CreatedColumns++;
		printf("[AUTO-DATABASE] [NEW] Created: %s.%s",
			RequiredColumns[column_index][col_table],
			RequiredColumns[column_index][col_name]
		);
	} else if (error_code == 1060) {
		// Column already exists (Duplicate column name)
		g_ExistingColumns++;
		printf("[AUTO-DATABASE] [OK] Exists: %s.%s",
			RequiredColumns[column_index][col_table],
			RequiredColumns[column_index][col_name]
		);
	} else {
		// Other error occurred
		g_FailedColumns++;
		new error_msg[256];
		mysql_error(error_msg, sizeof(error_msg), g_iHandle);
		printf("[AUTO-DATABASE] [ERR] Failed: %s.%s (Error: %d - %s)",
			RequiredColumns[column_index][col_table],
			RequiredColumns[column_index][col_name],
			error_code,
			error_msg
		);
	}
	
	// Move to next column
	g_ColumnIndex++;

	// Small delay to prevent MySQL overload
	SetTimer("AutoDatabase_CreateNextColumn", 150, false);
	
	return 1;
}

// Show summary of the auto-create process
forward AutoDatabase_ShowSummary();
public AutoDatabase_ShowSummary() {
	printf("========================================");
	printf("[AUTO-DATABASE] Process completed!");
	printf("Total columns checked: %d", g_TotalColumns);
	printf("Created: %d | Already existed: %d | Failed: %d", 
		g_CreatedColumns, g_ExistingColumns, g_FailedColumns);
	
	if (g_FailedColumns > 0) {
		printf("WARNING: %d columns failed to create. Check MySQL permissions and table structure.", g_FailedColumns);
	} else {
		printf("All database columns are now ready!");
	}
	printf("========================================");
	
	return 1;
}

// Hook to start auto-database when gamemode initializes
hook OnGameModeInit() {
	// Wait a bit for MySQL connection to be fully established
	SetTimer("AutoDatabase_Init", 3000, false);
	return 1;
}

// Function to add new required columns (for future use)
stock AutoDatabase_AddColumn(const table[], const column[], const type[], const default_value[]) {
	// This function can be used to dynamically add columns to the required list
	// For now, columns should be added to the RequiredColumns array above
	printf("[AUTO-DATABASE] Note: To add column %s.%s, add it to RequiredColumns array and restart server", table, column);
	return 1;
}

// Function to check if a specific column exists (utility function)
stock AutoDatabase_CheckColumn(const table[], const column[], const callback[]) {
	new query[256];
	format(query, sizeof(query),
		"SELECT COLUMN_NAME FROM INFORMATION_SCHEMA.COLUMNS WHERE TABLE_SCHEMA = DATABASE() AND TABLE_NAME = '%s' AND COLUMN_NAME = '%s'",
		table, column
	);
	mysql_tquery(g_iHandle, query, callback, "ss", table, column);
	return 1;
}

// Function to safely add a column (checks existence first)
stock AutoDatabase_SafeAddColumn(const table[], const column[], const type[], const default_value[]) {
	new query[512];
	format(query, sizeof(query),
		"SELECT COLUMN_NAME FROM INFORMATION_SCHEMA.COLUMNS WHERE TABLE_SCHEMA = DATABASE() AND TABLE_NAME = '%s' AND COLUMN_NAME = '%s'",
		table, column
	);

	new Cache:result = mysql_query(g_iHandle, query);
	new rows = cache_num_rows();
	cache_delete(result);

	if (rows == 0) {
		// Column doesn't exist, safe to create
		format(query, sizeof(query),
			"ALTER TABLE `%s` ADD COLUMN `%s` %s DEFAULT %s",
			table, column, type, default_value
		);
		mysql_tquery(g_iHandle, query);
		printf("[AUTO-DATABASE] ✓ Safe-Created: %s.%s", table, column);
		return 1;
	} else {
		// Column already exists
		printf("[AUTO-DATABASE] ✓ Safe-Skip: %s.%s (already exists)", table, column);
		return 0;
	}
}

// Function to handle duplicate column errors globally
stock AutoDatabase_HandleDuplicateError(const table[], const column[]) {
	printf("[AUTO-DATABASE] ⚠ Duplicate column detected: %s.%s", table, column);
	printf("[AUTO-DATABASE] ⚠ This column already exists in the database.");
	printf("[AUTO-DATABASE] ⚠ If you see this error outside auto-database system,");
	printf("[AUTO-DATABASE] ⚠ use AutoDatabase_SafeAddColumn() instead of direct ALTER TABLE.");
	return 1;
}
