/*
	Auto Database Column Creator
	Automatically creates missing database columns when server starts
	This prevents MySQL errors when new features are added but database isn't updated manually
*/

#include <YSI_Coding\y_hooks>

// Structure to define required database columns
enum ColumnInfo {
	col_table[32],
	col_name[32], 
	col_type[64],
	col_default[32]
}

// Define all required database columns here
// Add new columns to this array when adding new features
new RequiredColumns[][ColumnInfo] = {
	// Characters table columns - Player data
	{"characters", "last_mine_time", "INT(11)", "0"},

	// Cars table - Vehicle modification status (0/1)
	{"cars", "carNitro", "INT(11)", "0"},
	{"cars", "carHydraulic", "INT(11)", "0"},
	{"cars", "carSpoiler", "INT(11)", "0"},
	{"cars", "carFrontBumper", "INT(11)", "0"},
	{"cars", "carRearBumper", "INT(11)", "0"},
	{"cars", "carLeftSideskirt", "INT(11)", "0"},
	{"cars", "carRightSideskirt", "INT(11)", "0"},
	{"cars", "carRoof", "INT(11)", "0"},
	{"cars", "carExhaust", "INT(11)", "0"},

	// Cars table - Component IDs (actual SA-MP component numbers)
	{"cars", "carSpoilerID", "INT(11)", "0"},
	{"cars", "carFrontBumperID", "INT(11)", "0"},
	{"cars", "carRearBumperID", "INT(11)", "0"},
	{"cars", "carLeftSideskirtID", "INT(11)", "0"},
	{"cars", "carRightSideskirtID", "INT(11)", "0"},
	{"cars", "carRoofID", "INT(11)", "0"},
	{"cars", "carExhaustID", "INT(11)", "0"}

	// Add more columns here as needed:
	// {"table_name", "column_name", "column_type", "default_value"}
	// Example: {"characters", "new_feature_time", "INT(11)", "0"}
};

// Variables for tracking column creation process
new g_ColumnIndex = 0;
new g_TotalColumns = 0;
new g_CreatedColumns = 0;
new g_ExistingColumns = 0;
new g_FailedColumns = 0;

// Initialize auto database system
AutoDatabase_Init() {
	g_TotalColumns = sizeof(RequiredColumns);
	g_ColumnIndex = 0;
	g_CreatedColumns = 0;
	g_ExistingColumns = 0;
	g_FailedColumns = 0;
	
	printf("[AUTO-DATABASE] Starting auto-create process for %d columns...", g_TotalColumns);
	
	// Start the column creation process
	AutoDatabase_CreateNextColumn();
	
	return 1;
}

// Create the next column in the queue
AutoDatabase_CreateNextColumn() {
	if (g_ColumnIndex >= g_TotalColumns) {
		// All columns processed, show summary
		AutoDatabase_ShowSummary();
		return 1;
	}
	
	new query[256];
	format(query, sizeof(query), 
		"ALTER TABLE `%s` ADD COLUMN `%s` %s DEFAULT %s",
		RequiredColumns[g_ColumnIndex][col_table],
		RequiredColumns[g_ColumnIndex][col_name],
		RequiredColumns[g_ColumnIndex][col_type],
		RequiredColumns[g_ColumnIndex][col_default]
	);
	
	mysql_tquery(g_iHandle, query, "OnAutoColumnCreated", "d", g_ColumnIndex);
	
	return 1;
}

// Callback when a column creation attempt is completed
forward OnAutoColumnCreated(column_index);
public OnAutoColumnCreated(column_index) {
	new error_code = mysql_errno(g_iHandle);
	
	if (error_code == 0) {
		// Column created successfully
		g_CreatedColumns++;
		printf("[AUTO-DATABASE] ✓ Created: %s.%s", 
			RequiredColumns[column_index][col_table],
			RequiredColumns[column_index][col_name]
		);
	} else if (error_code == 1060) {
		// Column already exists (Duplicate column name)
		g_ExistingColumns++;
		printf("[AUTO-DATABASE] ✓ Exists: %s.%s", 
			RequiredColumns[column_index][col_table],
			RequiredColumns[column_index][col_name]
		);
	} else {
		// Other error occurred
		g_FailedColumns++;
		printf("[AUTO-DATABASE] ✗ Failed: %s.%s (Error: %d - %s)", 
			RequiredColumns[column_index][col_table],
			RequiredColumns[column_index][col_name],
			error_code,
			mysql_error(g_iHandle)
		);
	}
	
	// Move to next column
	g_ColumnIndex++;
	
	// Small delay to prevent MySQL overload
	SetTimer("AutoDatabase_CreateNextColumn", 100, false);
	
	return 1;
}

// Show summary of the auto-create process
AutoDatabase_ShowSummary() {
	printf("========================================");
	printf("[AUTO-DATABASE] Process completed!");
	printf("Total columns checked: %d", g_TotalColumns);
	printf("Created: %d | Already existed: %d | Failed: %d", 
		g_CreatedColumns, g_ExistingColumns, g_FailedColumns);
	
	if (g_FailedColumns > 0) {
		printf("WARNING: %d columns failed to create. Check MySQL permissions and table structure.", g_FailedColumns);
	} else {
		printf("All database columns are now ready!");
	}
	printf("========================================");
	
	return 1;
}

// Hook to start auto-database when gamemode initializes
hook OnGameModeInit() {
	// Wait a bit for MySQL connection to be fully established
	SetTimer("AutoDatabase_Init", 3000, false);
	return 1;
}

// Function to add new required columns (for future use)
stock AutoDatabase_AddColumn(const table[], const column[], const type[], const default_value[]) {
	// This function can be used to dynamically add columns to the required list
	// For now, columns should be added to the RequiredColumns array above
	printf("[AUTO-DATABASE] Note: To add column %s.%s, add it to RequiredColumns array and restart server", table, column);
	return 1;
}

// Function to check if a specific column exists (utility function)
stock AutoDatabase_CheckColumn(const table[], const column[], const callback[]) {
	new query[256];
	format(query, sizeof(query), 
		"SELECT COLUMN_NAME FROM INFORMATION_SCHEMA.COLUMNS WHERE TABLE_SCHEMA = DATABASE() AND TABLE_NAME = '%s' AND COLUMN_NAME = '%s'",
		table, column
	);
	mysql_tquery(g_iHandle, query, callback, "ss", table, column);
	return 1;
}
